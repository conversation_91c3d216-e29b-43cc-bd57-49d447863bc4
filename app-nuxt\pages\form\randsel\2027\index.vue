<script setup lang="ts">
import { ref, onMounted } from "vue";
import TheSchoolBagForm from "~~/components/form/school-bag/TheSchoolBagForm.vue";
import TheSchoolBagFormLogined from "~~/components/form/school-bag/TheSchoolBagFormLogined.vue";
import { useRuntimeConfig, navigateTo } from "#app";
import { useAuthStore } from "~~/src/stores/auth";
import Products from "~~/src/models/Products";
import Orders from "~~/src/models/Orders";
import Order from "~~/src/models/entry/Order";

import { useHead } from "unhead";

const isLoading = ref(true);

const authStore = useAuthStore();
const config = useRuntimeConfig();
const products = ref<TProduct[]>([]);



const isLogined = !!authStore.getAccessToken();
// const member = ref<Member>();
const orders = ref<Order[]>([]);

if (config.public.isProduction) {
    useHead({
        meta: [
            {
                name: "google-site-verification",
                content: "sd6BXRMajQVpR39NUe8I7YxWQyHFFqf0Qvpfx_0xKIQ",
            },
        ],
    });
}

// 初期化処理を行う
onMounted(async () => {
    // products取得処理
    Products.create(config)
        .getList()
        .then((productList) => {
            if (productList) {
                products.value = productList.map((product) => product.data);
            }
        });

    if (isLogined) {
        try {
            orders.value = await Orders.create(config).index();
            isLoading.value = false;
        } catch (error) {
            console.error("Error fetching orders:", error);
            authStore.clearAuth();
            navigateTo("/member/account/");
        }
    } else {
        isLoading.value = false;
    }
});
</script>

<template>
    <v-row justify="center">無料の2027年度ランドセルカタログを申し込みますか？</v-row>
    <v-row justify="center">
        <v-col cols="12" md="6" sm="12">
            <v-btn
                block
                color="green"
                size="large"
                @click="toConf"
            >
                はい
            </v-btn>
        </v-col>
    </v-row>
    <v-row justify="center">
        <v-col cols="4" md="2" sm="4" class="pr-2">
            <v-btn
                block
                color="blue"
                size="large"
                @click="toConf"
            >
                男の子向け
            </v-btn>
        </v-col>
        <v-col cols="4" md="2" sm="4" class="px-2">
            <v-btn
                block
                color="pink"
                size="large"
                @click="toConf"
            >
                女の子向け
            </v-btn>
        </v-col>
        <v-col cols="4" md="2" sm="4" class="pl-2">
            <v-btn
                block
                color="grey"
                size="large"
                @click="toConf"
            >
                両方
            </v-btn>
        </v-col>
    </v-row>
    <div v-if="!isLoading">
        <div v-if="isLogined">
            <the-school-bag-form-logined
                v-if="products.length > 0 && orders.length > 0"
                :products-data="products"
                :order-data="orders"
            />
        </div>
        <div v-else>
            <the-school-bag-form
                v-if="products.length"
                :products-data="products"
            />
        </div>
    </div>
    <div v-else class="coverme-progress-circular">
        <v-progress-circular indeterminate :size="50" />
    </div>
</template>

<style scoped>
.coverme-progress-circular {
    display: flex;
    justify-content: center;
    align-items: center;
}
</style>
